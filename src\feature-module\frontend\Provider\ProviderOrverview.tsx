import React from 'react';
import { FaRegStar, FaRegStarHalfStroke, FaStar } from 'react-icons/fa6';
import { IoBagHandleOutline, IoBookmarkOutline } from 'react-icons/io5';
import { VscTools } from 'react-icons/vsc';
import CustomCardWithHeader from '../../components/CustomCardWithHeader';
import CustomChip from '../../components/CustomChip';
import { FiUser } from 'react-icons/fi';
import moment from 'moment';
import { Address, ProviderData, ServiceData } from '../../../utils/type';
import ProviderServiceCard from '../../components/common/ui/ProviderServiceCard';
import StatusCard from '../../components/common/ui/StatusCard';

interface ProviderProps {
  service: ServiceData[];
  providerData: ProviderData;
}

const ProviderOrverview = ({ providerData, service }: ProviderProps) => {
  const InfoRow = ({
    icon,
    label,
    value,
  }: {
    icon?: React.ReactNode;
    label: string;
    value: string;
  }) => (
    <div className="flex items-center justify-between  py-2">
      <div className="flex items-center gap-3 text-sm text-gray-500">
        {icon}
        <span className="text-sm">{label}</span>
      </div>
      <p className="text-sm text-end">{value || '—'}</p>
    </div>
  );

  const formatAddress = (address?: Address) => {
    const parts = [
      address?.postalCode,
      address?.addressLine1,
      address?.city,
      address?.state,
      address?.country,
    ];

    return parts.filter(Boolean).join(', ');
  };

  return (
    <div>
      {/* Status */}
      <div className="grid grid-cols-5 gap-6 mt-10">
        <div className="col-span-3">
          <div className="grid grid-cols-5 gap-5">
            <StatusCard
              title="Rating (36)"
              value={4.5}
              isHoverEffect={true}
              icon={
                <div className="flex gap-1">
                  <FaStar className="text-xl fill-yellow-400" />
                  <FaStar className="text-xl fill-yellow-400" />
                  <FaStar className="text-xl fill-yellow-400" />
                  <FaRegStarHalfStroke className="text-xl fill-yellow-400" />
                  <FaRegStar className="text-xl text-gray-400" />
                </div>
              }
            />

            <StatusCard
              title="Jobs"
              isHoverEffect={true}
              value={178}
              icon={<VscTools className="text-2xl text-primary" />}
            />

            <StatusCard
              title="Total Service"
              isHoverEffect={true}
              value={18}
              icon={<IoBagHandleOutline className="text-2xl text-primary" />}
            />

            <StatusCard
              title="Inprogress Booking"
              isHoverEffect={true}
              value={27}
              icon={<IoBookmarkOutline className="text-2xl text-primary" />}
            />

            <StatusCard
              title="Staff"
              value={3}
              isHoverEffect={true}
              icon={<FiUser className="text-2xl text-primary" />}
            />
          </div>

          <CustomCardWithHeader
            title="About me"
            subtitle="Our values and commitment to excellence"
            isHeaderDevider={true}
            className="mt-6"
            mainContent={
              <p className="text-sm text-gray-600 leading-6 -mt-3">
                Before you can begin to determine what the composition of a
                particular paragraph will be, you must first decide on an
                argument and a working thesis statement for your paper. What is
                the most important idea that you are trying to convey to your
                reader? The information in each paragraph must be related to
                that idea. In other words, your paragraphs should remind your
                reader that there is a recurrent relationship between your
                thesis and the information in each paragraph. A working thesis
                functions like a seed from which your paper, and your ideas,
                will grow. The whole process is an organic one—a natural
                progression from a seed to a full-blown paper where there are
                direct, familial relationships between all of the ideas in the
                paper.
              </p>
            }
          />
        </div>

        <div className="col-span-2">
          <CustomCardWithHeader
            title="  Personal Information"
            subtitle=" personal details and contact information"
            isHeaderDevider={true}
            mainContent={
              <>
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-3 text-sm text-gray-500">
                    {/* {icon} */}
                    <span className="text-body1">Category</span>
                  </div>
                  <div className="space-x-1 space-y-1 max-w-lg">
                    <CustomChip
                      label={'Information technology'}
                      color="primary"
                      size="sm"
                      variant="flat"
                      className="text-xs "
                    />
                    <CustomChip
                      label={'Marketing'}
                      color="primary"
                      size="sm"
                      variant="flat"
                      className="text-xs "
                    />
                    <CustomChip
                      label={'Shoping'}
                      color="primary"
                      size="sm"
                      variant="flat"
                      className="text-xs "
                    />
                  </div>
                </div>
                <InfoRow
                  label="Account Type"
                  value={providerData?.IsBusiness ? 'Business' : 'Individual'}
                />
                <InfoRow label="Email" value={providerData?.email || '-'} />
                <InfoRow
                  label="Location"
                  value={formatAddress(providerData?.address) || '-'}
                />
                <InfoRow
                  label="Date of Join"
                  value={
                    moment(providerData?.createdAt).format('MMM DD, YYYY') ||
                    '-'
                  }
                />
              </>
            }
          />
        </div>
      </div>

      <div className="mt-10 ">
        <div className="flex flex-col items-start">
          <p className="text-md font-semibold text-gray-700 ">
            Services We Provide
          </p>
          <p className="text-xs text-gray-500 mb-2">
            Explore how our comprehensive services help you achieve your goals
            efficiently and effectively
          </p>
        </div>
        <div className="border-b"></div>

        <div className="mt-6 grid grid-cols-5 gap-6">
          {service.length > 0 ? (
            service.map((service: ServiceData, index: number) => (
              <div key={index}>
                <ProviderServiceCard data={service} />
              </div>
            ))
          ) : (
            <small className="col-span-5 justify-center items-center pl-5">
              No Service
            </small>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProviderOrverview;
