import React, { useEffect, useState } from 'react';
import { BiCheck, BiX } from 'react-icons/bi';
import { FaBell, FaCheckCircle, FaTrash } from 'react-icons/fa';

type Notification = {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
};

const sampleNotifications: Notification[] = [
  {
    id: '1',
    title: '✅ Booking Confirmed',
    message: 'Your plumbing service is scheduled for Sep 10 at 2:00 PM',
    time: '2 hours ago',
    read: false,
  },
  {
    id: '2',
    title: '⭐ New Review',
    message: '<PERSON> gave you 5 stars! "Excellent service, highly recommend"',
    time: '1 day ago',
    read: false,
  },
  {
    id: '3',
    title: '💳 Payment Received',
    message: 'Payment of $45.00 has been successfully processed',
    time: '3 days ago',
    read: true,
  },
  {
    id: '4',
    title: '🔔 Reminder',
    message: 'You have a service appointment tomorrow at 10:00 AM',
    time: '5 hours ago',
    read: false,
  },
];

const NotificationSettings: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [emailEnabled, setEmailEnabled] = useState<boolean>(() => {
    try {
      const raw = localStorage.getItem('notif_email_enabled');
      return raw ? JSON.parse(raw) : true;
    } catch {
      return true;
    }
  });
  const [pushEnabled, setPushEnabled] = useState<boolean>(() => {
    try {
      const raw = localStorage.getItem('notif_push_enabled');
      return raw ? JSON.parse(raw) : true;
    } catch {
      return true;
    }
  });

  useEffect(() => {
    // load sample notifications (replace with real API call when available)
    setNotifications(sampleNotifications);
  }, []);

  useEffect(() => {
    localStorage.setItem('notif_email_enabled', JSON.stringify(emailEnabled));
  }, [emailEnabled]);

  useEffect(() => {
    localStorage.setItem('notif_push_enabled', JSON.stringify(pushEnabled));
  }, [pushEnabled]);

  const toggleRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: !n.read } : n))
    );
  };

  const markAllRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const clearAll = () => setNotifications([]);

  const deleteNotification = (id: string) => {
    setNotifications((prev) => prev.filter(n => n.id !== id));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Simple Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <FaBell className="text-2xl text-primary" />
          <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
          {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {unreadCount} new
            </span>
          )}
        </div>
        <p className="text-gray-600">Stay updated with your latest activities</p>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-3 mb-6">
        <button
          onClick={markAllRead}
          disabled={unreadCount === 0}
          className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          <FaCheckCircle className="text-sm" />
          Mark All Read
        </button>
        <button
          onClick={clearAll}
          disabled={notifications.length === 0}
          className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          <FaTrash className="text-sm" />
          Clear All
        </button>
      </div>

      {/* Settings */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Notification Settings</h3>
        <div className="space-y-4">
          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={emailEnabled}
              onChange={() => setEmailEnabled((v) => !v)}
              className="w-5 h-5 text-primary border-gray-300 rounded focus:ring-primary"
            />
            <div>
              <span className="text-gray-900 font-medium">Email Notifications</span>
              <p className="text-sm text-gray-500">Receive notifications via email</p>
            </div>
          </label>

          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={pushEnabled}
              onChange={() => setPushEnabled((v) => !v)}
              className="w-5 h-5 text-primary border-gray-300 rounded focus:ring-primary"
            />
            <div>
              <span className="text-gray-900 font-medium">Push Notifications</span>
              <p className="text-sm text-gray-500">Receive notifications in your browser</p>
            </div>
          </label>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Recent Notifications</h3>
        </div>

        <div className="divide-y divide-gray-100">
          {notifications.length === 0 ? (
            <div className="text-center py-16">
              <FaBell className="mx-auto text-6xl text-gray-300 mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">All caught up!</h3>
              <p className="text-gray-500">No new notifications at the moment.</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 transition-colors ${
                  !notification.read ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start gap-4">
                  {/* Status Indicator */}
                  <div className="flex-shrink-0 mt-1">
                    {!notification.read ? (
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    ) : (
                      <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <h4 className={`text-base font-medium ${
                          !notification.read ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {notification.title}
                        </h4>
                        <p className="text-gray-600 mt-1 leading-relaxed">
                          {notification.message}
                        </p>
                        <p className="text-sm text-gray-500 mt-2">
                          {notification.time}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => toggleRead(notification.id)}
                          className={`p-2 rounded-lg transition-colors ${
                            notification.read
                              ? 'text-gray-400 hover:text-blue-500 hover:bg-blue-50'
                              : 'text-blue-500 hover:bg-blue-100'
                          }`}
                          title={notification.read ? 'Mark as unread' : 'Mark as read'}
                        >
                          {notification.read ? <BiCheck className="w-5 h-5" /> : <FaCheckCircle className="w-4 h-4" />}
                        </button>
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                          title="Delete notification"
                        >
                          <BiX className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
