import { Progress } from '@heroui/react';
import { FaStar } from 'react-icons/fa6';

const RatingDistribution = () => {
  return (
    <div className="space-y-2 -mt-3 ">
      {/* 5 */}
      <div className="flex justify-start items-center gap-2 ">
        <p className="text-xs">5</p>
        <FaStar className="text-xs fill-yellow-400" />
        <Progress aria-label="Loading..." size="sm" value={78} />
        <p className="text-xs">(108)</p>
      </div>

      {/* 4 */}
      <div className="flex justify-start items-center gap-2">
        <p className="text-xs">4</p>
        <FaStar className="text-xs fill-yellow-400" />
        <Progress aria-label="Loading..." size="sm" value={50} />
        <p className="text-xs">(23)</p>
      </div>

      {/* 3 */}
      <div className="flex justify-start items-center gap-2">
        <p className="text-xs">3</p>
        <FaStar className="text-xs fill-yellow-400" />
        <Progress aria-label="Loading..." size="sm" value={20} />
        <p className="text-xs">(12)</p>
      </div>

      {/* 2 */}
      <div className="flex justify-start items-center gap-2">
        <p className="text-xs">2</p>
        <FaStar className="text-xs fill-yellow-400" />
        <Progress aria-label="Loading..." size="sm" value={10} />
        <p className="text-xs">(32)</p>
      </div>

      {/* 1 */}
      <div className="flex justify-start items-center gap-2">
        <p className="text-xs">1</p>
        <FaStar className="text-xs fill-yellow-400" />
        <Progress aria-label="Loading..." size="sm" value={30} />
        <p className="text-xs">(12)</p>
      </div>
    </div>
  );
};

export default RatingDistribution;
