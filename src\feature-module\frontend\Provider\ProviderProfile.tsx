import { useEffect, useState } from 'react';
// import { useParams } from 'react-router-dom';
import CustomButton from '../../components/CustomButton';
import CustomChip from '../../components/CustomChip';
import { apiClient } from '../../../api';
import { ServiceData } from '../../../utils/type';
import ProviderOrverview from './ProviderOrverview';
import ProviderReview from './ProviderReview';
import TabNav from '../../components/common/ui/TabNav';

const ProviderProfile = () => {
  // const { id } = useParams();
  const [activeTab, setActiveTab] = useState('overview');
  const [service, setService] = useState<ServiceData[]>([]);

  // const { data, isFetching } = useFetchProviderById(id);
  // const { data: providerPaylaod } = useFetchServiceDataByProvider();

  useEffect(() => {
    const fetchAllService = async () => {
      try {
        const res = await apiClient.get(
          `${import.meta.env.VITE_APP_BACKEND_SERVICE}/service`
        );
        setService(res?.data?.services);
      } catch (error: unknown) {
        console.log('Error: ', error);
        throw error;
      }
    };
    fetchAllService();
  }, []);

  console.log('Data: Data: ', service);

  const tabs = [
    { id: 'overview', title: 'Overview' },
    { id: 'reviews', title: 'Reviews & Ratings' },
  ];

  const providerData = {
    userId: 'G_UID_73',
    IsIndividual: false,
    IsBusiness: false,
    enterpriseId: null,
    name: 'fefefe',
    email: '<EMAIL>',
    mobile: '+***********',
    dateOfBirth: '2000-02-04',
    groupRole: 'provider',
    bio: 'dvdvd',
    address: {
      addressLine1: 'dsdsdsdsd',
      country: 'Canada',
      state: 'Quebec',
      city: 'dsdsds',
      postalCode: 'A0A 0A0',
    },
    currencyCode: 'USD',
    language: 'French',
    profilePicture:
      'https://cdn.staging.gigmosaic.ca/provider/G_UID_73-Test/profile-picture/1756382877396-EuQb8U2zAekWXhTyYyqwRRj5D8B5uVKbZ0xL0ROJfrhLQEOYmnQsSJkvH6Hl0k5uO0IMdo2Zt1Nj6Crq9VtC4q8neEFSCe0N4ItIK5TDM8280JXtfuhpjZSjEgXjFvqY.jpg',
    ISVerified: true,
    providerStatus: 'approved',
    IsDocUploaded: true,
    IsDocReviewed: true,
    IsDocVerified: false,
    twoFactorEnabled: {
      IsTwoFactorEnabled: false,
      lastUpdatedAt: null,
    },
    password: {
      IsPasswordReset: false,
      lastUpdatedAt: null,
    },
    IsAgreed: false,
    IsActive: false,
    createdAt: '2025-07-31T05:40:29.413Z',
    updatedAt: '2025-08-30T08:29:56.876Z',
    __v: 0,
  };

  // if (isFetching)
  //   return (
  //     <>
  //       <LoadingSpinner />
  //     </>
  //   );

  return (
    <div>
      <div className="bg-[linear-gradient(90deg,hsla(196,100%,95%,1)_0%,hsla(0,0%,100%,1)_100%)] h-[200px] flex justify-end items-center rounded-xl shadow-l-sm mt-3"></div>

      {/* Profile */}
      <div className="flex -mt-12 justify-between">
        <div className="flex ml-5 ">
          <img
            src={providerData?.profilePicture}
            alt="Profile"
            className="rounded-full size-36 border-5 border-white"
          />

          {/* Profile name and other */}
          <div className="flex flex-col items-start mt-14 ml-4">
            <h1 className="text-2xl font-semibold text-gray-800">
              Auto Car Repaire
            </h1>

            <div className="mt-2">
              <CustomChip
                label={
                  providerData?.ISVerified
                    ? 'Verified Provider'
                    : 'Not Verified'
                }
                color={providerData?.ISVerified ? 'primary' : 'warning'}
                size="sm"
                variant="flat"
                className="text-xs font-semibold"
              />
            </div>
          </div>
        </div>

        {/* <div className="flex gap-6">
          <p className="text-body1 mt-16">
            Provider ID: {providerData.userId || '-'}
          </p>
          <p className="text-body1 mt-16">
            Provider ID: {providerData.userId || '-'}
          </p>
          <p className="text-body1 mt-16">
            Provider ID: {providerData.userId || '-'}
          </p>
        </div> */}

        {/* Button */}
        <div className="flex mt-16 ">
          <CustomButton
            label="Follow"
            color="primary"
            radius="sm"
            variant="flat"
          />
        </div>
      </div>

      {/* Tab Navigation */}
      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <ProviderOrverview providerData={providerData} service={service} />
      )}

      {activeTab === 'reviews' && <ProviderReview />}
    </div>
  );
};

export default ProviderProfile;
