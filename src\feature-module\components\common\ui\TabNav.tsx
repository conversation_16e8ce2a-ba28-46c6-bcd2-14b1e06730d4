// TabNav.tsx
import { ReactNode } from 'react';

interface TabButtonProps {
  id: string;
  title: string;
  icon?: ReactNode;
  isActive: boolean;
  onClick: (id: string) => void;
}

export const TabButton = ({
  id,
  title,
  icon,
  isActive,
  onClick,
}: TabButtonProps) => {
  return (
    <button
      onClick={() => onClick(id)}
      className={`flex items-center gap-2 py-3 px-1 text-sm font-medium transition-colors duration-200 ${
        isActive
          ? 'border-b-2 border-primary text-primary'
          : 'text-gray-500 hover:text-gray-700'
      }`}
    >
      {icon && <span className="text-lg">{icon}</span>}
      {title}
    </button>
  );
};

interface TabNavProps {
  activeTab: string;
  onTabChange: (id: string) => void;
  tabs: { id: string; title: string; icon?: ReactNode }[];
}

const TabNav = ({ activeTab, onTabChange, tabs }: TabNavProps) => {
  return (
    <div className="border-b border-gray-200 mt-5">
      <nav className="flex space-x-6">
        {tabs.map((tab) => (
          <TabButton
            key={tab.id}
            id={tab.id}
            title={tab.title}
            icon={tab.icon}
            isActive={activeTab === tab.id}
            onClick={onTabChange}
          />
        ))}
      </nav>
    </div>
  );
};

export default TabNav;
