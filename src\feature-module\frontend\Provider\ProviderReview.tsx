import { FaRegStar, FaRegStarHalfStroke, FaStar } from 'react-icons/fa6';
import StatusCard from '../../components/common/ui/StatusCard';
import CustomCardWithHeader from '../../components/CustomCardWithHeader';
import RatingDistribution from '../../components/common/ui/RatingDistribution';
import { useState } from 'react';
import ReviewsMessage from '../../components/common/ui/ReviewsMessage';
import TabNav from '../../components/common/ui/TabNav';

const ProviderReview = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'all', title: 'All Reviews' },
    { id: '1', title: '1 Star' },
    { id: '2', title: '2 Start' },
    { id: '3', title: '3 Start' },
    { id: '4', title: '4 Start' },
    { id: '5', title: '5 Start' },
  ];

  return (
    <div className="mt-6">
      <div className="grid grid-cols-6 gap-6">
        <StatusCard
          title="Rating (345)"
          value={4.5}
          className="col-span-2 border-b-1"
          icon={
            <div className="flex gap-1">
              <FaStar className="text-3xl fill-yellow-400" />
              <FaStar className="text-3xl fill-yellow-400" />
              <FaStar className="text-3xl fill-yellow-400" />
              <FaRegStarHalfStroke className="text-3xl fill-yellow-400" />
              <FaRegStar className="text-3xl text-gray-300" />
            </div>
          }
        />

        <CustomCardWithHeader
          title="Rating Distribution"
          isHeaderDevider={true}
          className="col-span-4 "
          mainContent={<RatingDistribution />}
        />
      </div>
      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />
      <div className="grid gap-3 mt-6">
        <ReviewsMessage />
        <ReviewsMessage />
        <ReviewsMessage />
        <ReviewsMessage />
      </div>
    </div>
  );
};

export default ProviderReview;
